import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import HorizontalWhiteDivider from '../HorizontalWhilteDivider';
import { AiOutlineMenu, AiOutlineClose } from 'react-icons/ai';

const menuItems = [
  { path: '/', label: 'Home', icon: 'HomeIcon.svg' },
  { path: '/users', label: 'User Management', icon: 'userManagementIcon.svg' },
  { path: '/expense-reports', label: 'Expense Reports', icon: 'expenseRepoersIcon.svg' },
  { path: '/policies', label: 'Policy Management', icon: 'policyManagementIcon.svg' },
  { path: '/categories', label: 'Categories', icon: 'categoriesIcon.svg' },
  { path: '/integrations', label: 'Integrations', icon: 'integrationIcon.svg' },
  { path: '/audit-trail', label: 'Audit Trail', icon: 'auditTrailIcon.svg' },
  { path: '/settings', label: 'Settings', icon: 'settingsIcon.svg' },
];

const Sidebar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleLogout = () => {
    console.log('Logout clicked');
    // Add logout logic here
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={toggleMobileMenu}
          className="p-2 rounded-md bg-slate-800 text-white hover:bg-slate-700"
        >
          {isMobileMenuOpen ? (
            <AiOutlineClose className="h-6 w-6" />
          ) : (
            <AiOutlineMenu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggleMobileMenu}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed lg:static inset-y-0 left-0 z-40
          w-64 brand-gradient text-white
          transform transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          flex flex-col
        `}
      >
        {/* Logo & Title */}
        <div className="flex flex-col items-center my-5">
          <img src="/brandLogo.svg" alt="brandLogo" className="h-11 mb-1" />
          <p className="text-white text-sm font-light tracking-wider">ADMIN DASHBOARD</p>
        </div>

        <HorizontalWhiteDivider />

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-0.5 text-sm text-gray-200">
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;

            return (
              <div key={item.path}>
                <Link
                  to={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className='flex items-center space-x-3 px-4 py-3 rounded-lg'
                >
                  <img src={item.icon} alt={item.label} className="h-4 w-4" />
                  <span className="font-medium">{item.label}</span>
                </Link>


                {(item.label === 'Expense Reports' || item.label === 'Settings') && (
                  <div className='my-5'>
                    <HorizontalWhiteDivider />
                  </div>
                )}

                {item.label === 'Settings' && (
                  <div className="px-4 py-4">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-200"
                    >
                      <img src="/logoutIcon.svg" className="h-5 w-5" alt="logout" />
                      <span className="font-medium">Log Out</span>
                    </button>
                  </div>
                )}

              </div>
            );
          })}
          <div className='my-5'>
            <HorizontalWhiteDivider />
          </div>
        </nav>






        {/* Upgrade Section */}
        <div className="px-4 pb-6">
          <div className="bg-slate-900 rounded-lg p-4 text-center">
            <div className="w-12 h-12 bg-white rounded-full mx-auto mb-3 flex items-center justify-center">
              <div className="w-8 h-8 bg-slate-800 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">👤</span>
              </div>
            </div>
            <h3 className="text-white font-semibold mb-1">Upgrade to PRO</h3>
            <p className="text-slate-400 text-xs mb-3">
              to get access to all features!<br />
              Connect with Expenso
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
